import Text6 from '@/components/CustomText';
import LanguageDropdown from '@/components/LanguageDropdown';

import AntDesign from '@expo/vector-icons/AntDesign';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable, ScrollView, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';

// Mock ingredient data for autocomplete
const MOCK_INGREDIENTS = [
  '양파',
  '당근',
  '감자',
  '토마토',
  '브로콜리',
  '시금치',
  '배추',
  '무',
  '대파',
  '마늘',
  '생강',
  '고추',
  '파프리카',
  '오이',
  '호박',
  '가지',
  '닭고기',
  '돼지고기',
  '소고기',
  '계란',
  '두부',
  '버섯',
  '콩나물',
  '숙주',
  'onion',
  'carrot',
  'potato',
  'tomato',
  'broccoli',
  'spinach',
  'cabbage',
  'radish',
  'green onion',
  'garlic',
  'ginger',
  'pepper',
  'bell pepper',
  'cucumber',
  'zucchini',
  'eggplant',
  'chicken',
  'pork',
  'beef',
  'egg',
  'tofu',
  'mushroom',
  'bean sprouts',
  'mung bean sprouts',
];

export default function MainScreen() {
  const { t } = useTranslation();
  const [searchText, setSearchText] = useState('');
  const [ingredients, setIngredients] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);

  const handleBookPress = () => {
    router.push('/recipe_list');
  };

  const handleSearchChange = (text: string) => {
    setSearchText(text);
    if (text.trim().length > 0) {
      const filtered = MOCK_INGREDIENTS.filter((ingredient) => ingredient.toLowerCase().includes(text.toLowerCase())).slice(0, 8); // Limit to 8 suggestions
      setFilteredSuggestions(filtered);
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const handleSuggestionPress = (ingredient: string) => {
    if (!ingredients.includes(ingredient) && ingredients.length < 5) {
      setIngredients([...ingredients, ingredient]);
    }
    setSearchText('');
    setShowSuggestions(false);
  };

  const handleRemoveIngredient = (index: number) => {
    setIngredients(ingredients.filter((_, i) => i !== index));
  };

  const handleSearch = () => {
    if (searchText.trim() && !ingredients.includes(searchText.trim()) && ingredients.length < 5) {
      setIngredients([...ingredients, searchText.trim()]);
      setSearchText('');
    }
    setShowSuggestions(false);
  };

  return (
    <View style={styles.container}>
      {/* Top row with two icons spaced between */}
      <View style={styles.topRow}>
        <LanguageDropdown iconSize={24} iconColor="#333" />
        <TouchableOpacity onPress={handleBookPress} activeOpacity={0.7}>
          <AntDesign name="book" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Header Text */}
        <View style={styles.headerContainer}>
          <Text6 weight="medium" style={styles.taglineText}>
            {t('main.tagline')}
          </Text6>
        </View>

        {/* Search Row */}
        <View style={styles.searchContainer}>
          <View style={styles.searchRow}>
            <TextInput
              style={styles.searchInput}
              placeholder={t('main.searchIngredients')}
              value={searchText}
              onChangeText={handleSearchChange}
              onFocus={() => {
                if (searchText.trim().length > 0) {
                  setShowSuggestions(true);
                }
              }}
            />
            <TouchableOpacity style={styles.searchButton} onPress={handleSearch} activeOpacity={0.8}>
              <AntDesign name="search1" size={20} color="#fff" />
            </TouchableOpacity>
          </View>

          {/* Auto-complete Suggestions */}
          {showSuggestions && filteredSuggestions.length > 0 && (
            <View style={styles.suggestionsContainer}>
              {filteredSuggestions.map((suggestion, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.suggestionItem}
                  onPress={() => handleSuggestionPress(suggestion)}
                  activeOpacity={0.7}
                >
                  <Text6 style={styles.suggestionText}>{suggestion}</Text6>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Selected Ingredients Display */}
        {ingredients.length > 0 && (
          <View style={styles.ingredientsContainer}>
            <View style={styles.ingredientsGrid}>
              {ingredients.map((ingredient, index) => (
                <View key={index} style={styles.ingredientBadge}>
                  <Text6 style={styles.ingredientText}>{ingredient}</Text6>
                  <TouchableOpacity onPress={() => handleRemoveIngredient(index)} style={styles.removeButton} activeOpacity={0.7}>
                    <AntDesign name="close" size={14} color="#666" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Spacer for ad banner */}
        <View style={styles.contentSpacer} />
      </ScrollView>

      {/* Google Ads Banner */}
      <View style={styles.adBanner}>
        <Text6 style={styles.adPlaceholder}>Google Ads Banner</Text6>
      </View>

      {/* Overlay to close suggestions */}
      {showSuggestions && <Pressable style={styles.overlay} onPress={() => setShowSuggestions(false)} />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60, // Safe area padding for status bar
    paddingBottom: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  headerContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    alignItems: 'center',
  },
  taglineText: {
    fontSize: 24,
    color: '#333',
    textAlign: 'center',
    lineHeight: 32,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
    position: 'relative',
    zIndex: 1000,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 12,
    color: '#333',
  },
  searchButton: {
    backgroundColor: '#FB9E3A',
    borderRadius: 8,
    padding: 10,
    marginLeft: 8,
  },
  suggestionsContainer: {
    position: 'absolute',
    top: 60,
    left: 20,
    right: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 1001,
  },
  suggestionItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: 'white',
    borderRadius: 8,
    marginHorizontal: 4,
    marginVertical: 2,
  },
  suggestionText: {
    fontSize: 16,
    color: '#333',
  },
  ingredientsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  ingredientsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  ingredientBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF5E6',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#FB9E3A',
  },
  ingredientText: {
    fontSize: 14,
    color: '#FB9E3A',
    marginRight: 6,
  },
  removeButton: {
    padding: 2,
  },
  contentSpacer: {
    height: 100, // Space for ad banner
  },
  adBanner: {
    height: 80,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  adPlaceholder: {
    fontSize: 16,
    color: '#666',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 999,
  },
});
